'use client'

import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useTranslations } from 'next-intl'

interface HowToGuideSectionProps {
  toolUrl: string
}

const HowToGuideSection: React.FC<HowToGuideSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')
  const [openStep, setOpenStep] = useState(1)

  const steps = [
    {
      id: 1,
      title: t('newStep1Title'),
      description: t('newStep1Description'),
      stepNumber: '1',
    },
    {
      id: 2,
      title: t('newStep2Title'),
      description: t('newStep2Description'),
      stepNumber: '2',
    },
  ]

  return (
    <section className="mx-auto flex max-w-screen-lg flex-col items-center px-5 my-32 xl:px-0 bg-slate-900">
      <div className="mx-auto mb-12 flex w-full flex-col gap-2">
        <h2 className="text-4xl font-normal text-white lg:text-5xl">
          {t('newHowToTitle')}
        </h2>
      </div>

      <div className="grid overflow-hidden md:grid-cols-[280px_1fr] md:rounded-lg lg:grid-cols-[350px_1fr]">
        {/* 左侧步骤列表 */}
        <div className="order-2 hidden max-w-full bg-slate-800 p-8 md:order-1 md:block md:max-w-[280px] lg:max-w-[350px]">
          <div className="w-full">
            {steps.map((step) => (
              <div key={step.id} className="mb-3">
                <h3 className="w-full">
                  <button
                    type="button"
                    onClick={() =>
                      setOpenStep(openStep === step.id ? 0 : step.id)
                    }
                    className="flex w-full items-center justify-between gap-3 py-2 text-left text-base font-semibold leading-normal text-white"
                  >
                    <span>
                      {step.id}. {step.title}
                    </span>
                    {openStep === step.id ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </button>
                </h3>
                {openStep === step.id && (
                  <div className="transform-gpu overflow-hidden leading-normal text-gray-300 mt-2">
                    <div className="text-base">{step.description}</div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 移动端步骤列表 */}
        <div className="order-2 block max-w-full pt-8 md:hidden">
          <div className="flex w-full flex-col gap-12">
            {steps.map((step) => (
              <div key={step.id}>
                <h3 className="text-2xl font-normal leading-normal text-white">
                  {step.title}
                </h3>
                <p className="mt-3 text-base leading-relaxed text-gray-300">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧模拟界面 */}
        <div className="relative order-1 h-auto w-full md:order-2 md:h-full bg-slate-700 rounded-lg overflow-hidden">
          {/* 模拟的编辑器界面 */}
          <div className="relative w-full h-[400px] md:h-[500px] bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900 p-4">
            {/* 顶部工具栏 */}
            <div className="flex items-center justify-between mb-4 bg-slate-800/50 rounded-lg px-3 py-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full shadow-sm"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full shadow-sm"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full shadow-sm"></div>
              </div>
              <div className="text-xs text-gray-300 font-medium">
                AI Image Extender
              </div>
            </div>

            {/* 主画布区域 */}
            <div className="relative w-full h-[calc(100%-60px)] bg-slate-600/80 rounded-lg border border-slate-500/50 flex items-center justify-center overflow-hidden">
              {/* 画布背景网格效果 */}
              <div
                className="absolute inset-0 opacity-10"
                style={{
                  backgroundImage: `
                       linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                       linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
                     `,
                  backgroundSize: '20px 20px',
                }}
              ></div>

              {/* 四个角的调整手柄 */}
              <div className="absolute top-3 left-3 w-3 h-3 border-2 border-blue-400 bg-blue-500 rounded-sm cursor-nw-resize shadow-lg hover:scale-110 transition-transform"></div>
              <div className="absolute top-3 right-3 w-3 h-3 border-2 border-blue-400 bg-blue-500 rounded-sm cursor-ne-resize shadow-lg hover:scale-110 transition-transform"></div>
              <div className="absolute bottom-3 left-3 w-3 h-3 border-2 border-blue-400 bg-blue-500 rounded-sm cursor-sw-resize shadow-lg hover:scale-110 transition-transform"></div>
              <div className="absolute bottom-3 right-3 w-3 h-3 border-2 border-blue-400 bg-blue-500 rounded-sm cursor-se-resize shadow-lg hover:scale-110 transition-transform"></div>

              {/* 中间的图片区域 - 保留真实图片 */}
              <div className="relative w-40 h-28 md:w-48 md:h-32 bg-black rounded-lg overflow-hidden border-2 border-white/20 shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&crop=face"
                  alt="Sample portrait for image extension"
                  className="w-full h-full object-cover"
                />
                {/* 图片上的选择框效果 */}
                <div className="absolute inset-0 border-2 border-dashed border-blue-400/60 rounded-lg"></div>
              </div>

              {/* 扩展区域提示 */}
              <div className="absolute inset-0 pointer-events-none">
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-56 h-36 md:w-64 md:h-40 border-2 border-dashed border-gray-400/30 rounded-lg"></div>
              </div>
            </div>

            {/* 右侧比例选择器 */}
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex flex-col gap-2">
              <div className="bg-slate-800/80 backdrop-blur-sm rounded-lg p-2 border border-slate-600/50 shadow-lg hover:bg-slate-700/80 transition-colors cursor-pointer">
                <div className="w-6 h-6 bg-slate-600 rounded border border-slate-500 flex items-center justify-center">
                  <div className="w-3 h-3 bg-blue-400 rounded-sm"></div>
                </div>
                <div className="text-xs text-gray-300 mt-1 text-center font-medium">
                  1:1
                </div>
              </div>
              <div className="bg-slate-800/80 backdrop-blur-sm rounded-lg p-2 border border-slate-600/50 shadow-lg hover:bg-slate-700/80 transition-colors cursor-pointer">
                <div className="w-6 h-4 bg-slate-600 rounded border border-slate-500 flex items-center justify-center">
                  <div className="w-4 h-2 bg-blue-400 rounded-sm"></div>
                </div>
                <div className="text-xs text-gray-300 mt-1 text-center font-medium">
                  4:3
                </div>
              </div>
              <div className="bg-slate-800/80 backdrop-blur-sm rounded-lg p-2 border border-slate-600/50 shadow-lg hover:bg-slate-700/80 transition-colors cursor-pointer">
                <div className="w-6 h-3 bg-slate-600 rounded border border-slate-500 flex items-center justify-center">
                  <div className="w-5 h-2 bg-blue-400 rounded-sm"></div>
                </div>
                <div className="text-xs text-gray-300 mt-1 text-center font-medium">
                  16:9
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA按钮 */}
      <a
        href={toolUrl}
        className="flex items-center justify-center gap-2 font-semibold transition duration-150 ease-in-out disabled:cursor-not-allowed disabled:opacity-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:outline-none text-nowrap text-base px-6 h-12 rounded bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 mt-12"
      >
        {t('howToCTAButton')}
      </a>
    </section>
  )
}

export default HowToGuideSection
