'use client'

import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useTranslations } from 'next-intl'

interface HowToGuideSectionProps {
  toolUrl: string
}

const HowToGuideSection: React.FC<HowToGuideSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')
  const [openStep, setOpenStep] = useState(1)

  const steps = [
    {
      id: 1,
      title: t('newStep1Title'),
      description: t('newStep1Description'),
      stepNumber: '1',
    },
    {
      id: 2,
      title: t('newStep2Title'),
      description: t('newStep2Description'),
      stepNumber: '2',
    },
  ]

  return (
    <section className="mx-auto flex max-w-screen-lg flex-col items-center px-5 my-32 xl:px-0 bg-slate-900">
      <div className="mx-auto mb-12 flex w-full flex-col gap-2">
        <h2 className="text-4xl font-normal text-white lg:text-5xl">
          {t('newHowToTitle')}
        </h2>
      </div>

      <div className="grid w-full overflow-hidden md:grid-cols-[280px_1fr] md:rounded-lg lg:grid-cols-[350px_1fr]">
        {/* 左侧步骤列表 */}
        <div className="order-2 hidden max-w-full bg-slate-800 p-8 md:order-1 md:block md:max-w-[280px] lg:max-w-[350px]">
          <div className="w-full">
            {steps.map((step) => (
              <div key={step.id} className="mb-3">
                <h3 className="w-full">
                  <button
                    type="button"
                    onClick={() =>
                      setOpenStep(openStep === step.id ? 0 : step.id)
                    }
                    className="flex w-full items-center justify-between gap-3 py-2 text-left text-base font-semibold leading-normal text-white"
                  >
                    <span>
                      {step.id}. {step.title}
                    </span>
                    {openStep === step.id ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </button>
                </h3>
                {openStep === step.id && (
                  <div className="transform-gpu overflow-hidden leading-normal text-gray-300 mt-2">
                    <div className="text-base">{step.description}</div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 移动端步骤列表 */}
        <div className="order-2 block max-w-full flex-1 pt-8 md:hidden">
          <div className="flex w-full flex-col gap-12">
            {steps.map((step) => (
              <div key={step.id}>
                <h3 className="text-2xl font-normal leading-normal text-white">
                  {step.title}
                </h3>
                <p className="mt-3 text-base leading-relaxed text-gray-300">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧模拟界面 */}
        <div className="relative order-1 h-auto w-full md:order-2 md:h-full bg-gray-900 rounded-r-lg overflow-hidden">
          {/* 模拟的图像编辑器界面 */}
          <div className="relative w-full h-[400px] md:h-[500px] bg-gray-900 p-6">
            {/* 主画布区域 */}
            <div className="relative w-full h-full bg-gray-800 rounded-lg flex items-center justify-center overflow-hidden">
              {/* 点状网格背景 */}
              <div
                className="absolute inset-0 opacity-20"
                style={{
                  backgroundImage: `radial-gradient(circle, rgba(255,255,255,0.3) 1px, transparent 1px)`,
                  backgroundSize: '20px 20px',
                }}
              ></div>

              {/* 四个角的L形裁剪框架 */}
              {/* 左上角 */}
              <div className="absolute top-4 left-4">
                <div className="w-6 h-1 bg-white"></div>
                <div className="w-1 h-6 bg-white"></div>
              </div>
              {/* 右上角 */}
              <div className="absolute top-4 right-4">
                <div className="w-6 h-1 bg-white ml-auto"></div>
                <div className="w-1 h-6 bg-white ml-auto"></div>
              </div>
              {/* 左下角 */}
              <div className="absolute bottom-4 left-4">
                <div className="w-1 h-6 bg-white"></div>
                <div className="w-6 h-1 bg-white"></div>
              </div>
              {/* 右下角 */}
              <div className="absolute bottom-4 right-4">
                <div className="w-1 h-6 bg-white ml-auto"></div>
                <div className="w-6 h-1 bg-white ml-auto"></div>
              </div>

              {/* 顶部和底部的调整手柄 */}
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"></div>
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"></div>

              {/* 左侧和右侧的调整手柄 */}
              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-full"></div>
              <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-full"></div>

              {/* 中间的图片区域 */}
              <div className="relative w-64 h-80 bg-black rounded-lg overflow-hidden shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=500&fit=crop&crop=face"
                  alt="Sample portrait for image extension"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* 右侧比例选择器 */}
            <div className="absolute right-6 top-1/2 transform -translate-y-1/2 flex flex-col gap-3">
              <div className="bg-gray-800 rounded-lg p-3 border border-gray-700 shadow-lg hover:bg-gray-700 transition-colors cursor-pointer">
                <div className="w-8 h-8 bg-gray-700 rounded border border-gray-600 flex items-center justify-center mb-2">
                  <div className="w-4 h-4 bg-white rounded-sm"></div>
                </div>
                <div className="text-xs text-gray-300 text-center font-medium">
                  1:1
                </div>
              </div>
              <div className="bg-gray-800 rounded-lg p-3 border border-gray-700 shadow-lg hover:bg-gray-700 transition-colors cursor-pointer">
                <div className="w-8 h-6 bg-gray-700 rounded border border-gray-600 flex items-center justify-center mb-2">
                  <div className="w-5 h-3 bg-white rounded-sm"></div>
                </div>
                <div className="text-xs text-gray-300 text-center font-medium">
                  4:3
                </div>
              </div>
              <div className="bg-gray-800 rounded-lg p-3 border border-gray-700 shadow-lg hover:bg-gray-700 transition-colors cursor-pointer">
                <div className="w-8 h-4 bg-gray-700 rounded border border-gray-600 flex items-center justify-center mb-2">
                  <div className="w-6 h-2 bg-white rounded-sm"></div>
                </div>
                <div className="text-xs text-gray-300 text-center font-medium">
                  16:9
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA按钮 */}
      <a
        href={toolUrl}
        className="flex items-center justify-center gap-2 font-semibold transition duration-150 ease-in-out disabled:cursor-not-allowed disabled:opacity-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:outline-none text-nowrap text-base px-6 h-12 rounded bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 mt-12"
      >
        {t('howToCTAButton')}
      </a>
    </section>
  )
}

export default HowToGuideSection
