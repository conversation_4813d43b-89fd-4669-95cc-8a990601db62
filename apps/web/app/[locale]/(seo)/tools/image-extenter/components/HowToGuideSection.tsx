'use client'

import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useTranslations } from 'next-intl'

interface HowToGuideSectionProps {
  toolUrl: string
}

const HowToGuideSection: React.FC<HowToGuideSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')
  const [openStep, setOpenStep] = useState(1)

  const steps = [
    {
      id: 1,
      title: t('step1Title'),
      description: t('step1Description'),
    },
    {
      id: 2,
      title: t('step2Title'),
      description: t('step2Description'),
    },
    {
      id: 3,
      title: t('step3Title'),
      description: t('step3Description'),
    },
    {
      id: 4,
      title: t('step4Title'),
      description: t('step4Description'),
    },
  ]

  return (
    <section className="mx-auto flex max-w-screen-lg flex-col items-center px-5 my-32 xl:px-0 bg-slate-900">
      <div className="mx-auto mb-12 flex w-full flex-col gap-2">
        <h2 className="text-4xl font-normal text-white lg:text-5xl">
          {t('newHowToTitle')}
        </h2>
      </div>

      <div className="grid overflow-hidden md:grid-cols-[280px_1fr] md:rounded-lg lg:grid-cols-[350px_1fr]">
        {/* 左侧步骤列表 */}
        <div className="order-2 hidden max-w-full bg-slate-800 p-8 md:order-1 md:block md:max-w-[280px] lg:max-w-[350px]">
          <div className="w-full">
            {steps.map((step) => (
              <div key={step.id} className="mb-3">
                <h3 className="w-full">
                  <button
                    type="button"
                    onClick={() =>
                      setOpenStep(openStep === step.id ? 0 : step.id)
                    }
                    className="flex w-full items-center justify-between gap-3 py-2 text-left text-base font-semibold leading-normal text-white"
                  >
                    <span>
                      {step.id}. {step.title}
                    </span>
                    {openStep === step.id ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </button>
                </h3>
                {openStep === step.id && (
                  <div className="transform-gpu overflow-hidden leading-normal text-gray-300 mt-2">
                    <div className="text-base">{step.description}</div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 移动端步骤列表 */}
        <div className="order-2 block max-w-full pt-8 md:hidden">
          <div className="flex w-full flex-col gap-12">
            {steps.map((step) => (
              <div key={step.id}>
                <h3 className="text-2xl font-normal leading-normal text-white">
                  {step.title}
                </h3>
                <p className="mt-3 text-base leading-relaxed text-gray-300">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧模拟界面 */}
        <div className="relative order-1 h-auto w-full md:order-2 md:h-full bg-slate-700 rounded-lg overflow-hidden">
          {/* 模拟的编辑器界面 */}
          <div className="relative w-full h-[400px] md:h-[500px] bg-gradient-to-br from-slate-800 to-slate-900 p-6">
            {/* 顶部工具栏 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <div className="text-xs text-gray-400">AI Image Extender</div>
            </div>

            {/* 画布区域 */}
            <div className="relative w-full h-full bg-slate-600 rounded-lg border-2 border-dashed border-gray-500 flex items-center justify-center">
              {/* 四个角的调整手柄 */}
              <div className="absolute top-2 left-2 w-3 h-3 border-2 border-white bg-blue-500 rounded-sm cursor-nw-resize"></div>
              <div className="absolute top-2 right-2 w-3 h-3 border-2 border-white bg-blue-500 rounded-sm cursor-ne-resize"></div>
              <div className="absolute bottom-2 left-2 w-3 h-3 border-2 border-white bg-blue-500 rounded-sm cursor-sw-resize"></div>
              <div className="absolute bottom-2 right-2 w-3 h-3 border-2 border-white bg-blue-500 rounded-sm cursor-se-resize"></div>

              {/* 中间的实际图片 */}
              <div className="relative w-48 h-32 md:w-56 md:h-36 bg-black rounded-lg overflow-hidden border-2 border-red-400">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&crop=face"
                  alt="Sample portrait for image extension"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* 右侧比例选择器 */}
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col gap-2">
              <div className="bg-slate-700 rounded-lg p-2 border border-gray-600">
                <div className="w-6 h-6 bg-gray-400 rounded flex items-center justify-center">
                  <div className="w-3 h-3 bg-white rounded-sm"></div>
                </div>
                <div className="text-xs text-gray-300 mt-1 text-center">
                  1:1
                </div>
              </div>
              <div className="bg-slate-700 rounded-lg p-2 border border-gray-600">
                <div className="w-6 h-4 bg-gray-400 rounded flex items-center justify-center">
                  <div className="w-4 h-2 bg-white rounded-sm"></div>
                </div>
                <div className="text-xs text-gray-300 mt-1 text-center">
                  4:3
                </div>
              </div>
              <div className="bg-slate-700 rounded-lg p-2 border border-gray-600">
                <div className="w-6 h-3 bg-gray-400 rounded flex items-center justify-center">
                  <div className="w-5 h-2 bg-white rounded-sm"></div>
                </div>
                <div className="text-xs text-gray-300 mt-1 text-center">
                  16:9
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA按钮 */}
      <a
        href={toolUrl}
        className="flex items-center justify-center gap-2 font-semibold transition duration-150 ease-in-out disabled:cursor-not-allowed disabled:opacity-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:outline-none text-nowrap text-base px-6 h-12 rounded bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 mt-12"
      >
        {t('howToCTAButton')}
      </a>
    </section>
  )
}

export default HowToGuideSection
