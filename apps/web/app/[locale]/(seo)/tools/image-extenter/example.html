<section
  class="mx-auto flex max-w-screen-lg flex-col items-center px-5 my-32 xl:px-0 bg-surface-background-0 xs:py-0"
>
  <div class="mx-auto mb-12 flex w-full flex-col gap-2">
    <h2 class="text-4xl font-normal text-surface-foreground-0 lg:text-5xl">
      How to expand images instantly
    </h2>
  </div>
  <div
    class="grid overflow-hidden md:grid-cols-[280px_1fr] md:rounded-lg lg:grid-cols-[350px_1fr]"
  >
    <div
      class="order-2 hidden max-w-full bg-surface-1 p-8 md:order-1 md:block md:max-w-[280px] lg:max-w-[350px]"
    >
      <div class="" data-testid="accordion-container">
        <div class="w-full" data-orientation="vertical">
          <div data-state="open" data-orientation="vertical" class="mb-3">
            <h3 data-orientation="vertical" data-state="open" class="w-full">
              <button
                type="button"
                aria-controls="radix-:Rip6sd6:"
                aria-expanded="true"
                data-state="open"
                data-orientation="vertical"
                id="radix-:R2p6sd6:"
                class="flex w-full items-center justify-between gap-3 py-2 text-left text-base font-semibold leading-normal text-surface-foreground-0"
                data-radix-collection-item=""
              >
                <span
                  >1.
                  <!-- -->Upload an image</span
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                  width="16"
                  height="16"
                  aria-hidden="true"
                  class="$w-[1em] $h-[1em] $fill-current $text-lg"
                >
                  <path
                    d="m256 236.4 92.3 92.3c9.8 9.8 25.6 9.8 35.4 0s9.8-25.6 0-35.4l-110-110c-4.9-4.9-11.3-7.3-17.7-7.3s-12.8 2.4-17.7 7.3l-110 110c-9.8 9.8-9.8 25.6 0 35.4s25.6 9.8 35.4 0z"
                  ></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="open"
              id="radix-:Rip6sd6:"
              role="region"
              aria-labelledby="radix-:R2p6sd6:"
              data-orientation="vertical"
              class="transform-gpu overflow-hidden leading-normal text-surface-foreground-2 will-change-[height] data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
                --radix-collapsible-content-height: 51.1875px;
                --radix-collapsible-content-width: 286px;
              "
            >
              <div class="text-base">
                Add your image or generate one using the
                <a
                  class="cursor-pointer no-underline text-surface-foreground-accent-0 hover:text-piki-blue-400"
                  href="https://www.freepik.com/ai/image-generator"
                  >AI Image Generator</a
                >.
              </div>
            </div>
          </div>
          <div data-state="closed" data-orientation="vertical" class="mb-3">
            <h3 data-orientation="vertical" data-state="closed" class="w-full">
              <button
                type="button"
                aria-controls="radix-:Rkp6sd6:"
                aria-expanded="false"
                data-state="closed"
                data-orientation="vertical"
                id="radix-:R4p6sd6:"
                class="flex w-full items-center justify-between gap-3 py-2 text-left text-base font-semibold leading-normal text-surface-foreground-0"
                data-radix-collection-item=""
              >
                <span
                  >2.
                  <!-- -->Choose the aspect ratio</span
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                  width="16"
                  height="16"
                  aria-hidden="true"
                  class="$w-[1em] $h-[1em] $fill-current $text-lg"
                >
                  <path
                    d="m256 275.6-92.3-92.3c-9.8-9.8-25.6-9.8-35.4 0s-9.8 25.6 0 35.4l110 110c4.9 4.9 11.3 7.3 17.7 7.3s12.8-2.4 17.7-7.3l110-110c9.8-9.8 9.8-25.6 0-35.4s-25.6-9.8-35.4 0z"
                  ></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="closed"
              id="radix-:Rkp6sd6:"
              role="region"
              aria-labelledby="radix-:R4p6sd6:"
              data-orientation="vertical"
              class="transform-gpu overflow-hidden leading-normal text-surface-foreground-2 will-change-[height] data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
                --radix-collapsible-content-height: 76.78125px;
                --radix-collapsible-content-width: 286px;
              "
              hidden=""
            ></div>
          </div>
          <div data-state="closed" data-orientation="vertical" class="mb-3">
            <h3 data-orientation="vertical" data-state="closed" class="w-full">
              <button
                type="button"
                aria-controls="radix-:Rmp6sd6:"
                aria-expanded="false"
                data-state="closed"
                data-orientation="vertical"
                id="radix-:R6p6sd6:"
                class="flex w-full items-center justify-between gap-3 py-2 text-left text-base font-semibold leading-normal text-surface-foreground-0"
                data-radix-collection-item=""
              >
                <span
                  >3.
                  <!-- -->Add details in your prompt</span
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                  width="16"
                  height="16"
                  aria-hidden="true"
                  class="$w-[1em] $h-[1em] $fill-current $text-lg"
                >
                  <path
                    d="m256 275.6-92.3-92.3c-9.8-9.8-25.6-9.8-35.4 0s-9.8 25.6 0 35.4l110 110c4.9 4.9 11.3 7.3 17.7 7.3s12.8-2.4 17.7-7.3l110-110c9.8-9.8 9.8-25.6 0-35.4s-25.6-9.8-35.4 0z"
                  ></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="closed"
              id="radix-:Rmp6sd6:"
              hidden=""
              role="region"
              aria-labelledby="radix-:R6p6sd6:"
              data-orientation="vertical"
              class="transform-gpu overflow-hidden leading-normal text-surface-foreground-2 will-change-[height] data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
              "
            ></div>
          </div>
          <div data-state="closed" data-orientation="vertical" class="mb-3">
            <h3 data-orientation="vertical" data-state="closed" class="w-full">
              <button
                type="button"
                aria-controls="radix-:Rop6sd6:"
                aria-expanded="false"
                data-state="closed"
                data-orientation="vertical"
                id="radix-:R8p6sd6:"
                class="flex w-full items-center justify-between gap-3 py-2 text-left text-base font-semibold leading-normal text-surface-foreground-0"
                data-radix-collection-item=""
              >
                <span
                  >4.
                  <!-- -->Click to expand</span
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                  width="16"
                  height="16"
                  aria-hidden="true"
                  class="$w-[1em] $h-[1em] $fill-current $text-lg"
                >
                  <path
                    d="m256 275.6-92.3-92.3c-9.8-9.8-25.6-9.8-35.4 0s-9.8 25.6 0 35.4l110 110c4.9 4.9 11.3 7.3 17.7 7.3s12.8-2.4 17.7-7.3l110-110c9.8-9.8 9.8-25.6 0-35.4s-25.6-9.8-35.4 0z"
                  ></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="closed"
              id="radix-:Rop6sd6:"
              hidden=""
              role="region"
              aria-labelledby="radix-:R8p6sd6:"
              data-orientation="vertical"
              class="transform-gpu overflow-hidden leading-normal text-surface-foreground-2 will-change-[height] data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
              "
            ></div>
          </div>
          <div data-state="closed" data-orientation="vertical" class="mb-3">
            <h3 data-orientation="vertical" data-state="closed" class="w-full">
              <button
                type="button"
                aria-controls="radix-:Rqp6sd6:"
                aria-expanded="false"
                data-state="closed"
                data-orientation="vertical"
                id="radix-:Rap6sd6:"
                class="flex w-full items-center justify-between gap-3 py-2 text-left text-base font-semibold leading-normal text-surface-foreground-0"
                data-radix-collection-item=""
              >
                <span
                  >5.
                  <!-- -->Download or upscale</span
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                  width="16"
                  height="16"
                  aria-hidden="true"
                  class="$w-[1em] $h-[1em] $fill-current $text-lg"
                >
                  <path
                    d="m256 275.6-92.3-92.3c-9.8-9.8-25.6-9.8-35.4 0s-9.8 25.6 0 35.4l110 110c4.9 4.9 11.3 7.3 17.7 7.3s12.8-2.4 17.7-7.3l110-110c9.8-9.8 9.8-25.6 0-35.4s-25.6-9.8-35.4 0z"
                  ></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="closed"
              id="radix-:Rqp6sd6:"
              hidden=""
              role="region"
              aria-labelledby="radix-:Rap6sd6:"
              data-orientation="vertical"
              class="transform-gpu overflow-hidden leading-normal text-surface-foreground-2 will-change-[height] data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
              "
            ></div>
          </div>
        </div>
      </div>
    </div>
    <div class="order-2 block max-w-full pt-8 md:hidden">
      <div class="flex w-full flex-col gap-12">
        <div>
          <h3
            class="text-2xl font-normal leading-normal text-surface-foreground-0"
          >
            Upload an image
          </h3>
          <p class="mt-3 text-base leading-relaxed text-surface-foreground-2">
            Add your image or generate one using the
            <a
              class="cursor-pointer no-underline text-surface-foreground-accent-0 hover:text-piki-blue-400"
              href="https://www.freepik.com/ai/image-generator"
              >AI Image Generator</a
            >.
          </p>
        </div>
        <div>
          <h3
            class="text-2xl font-normal leading-normal text-surface-foreground-0"
          >
            Choose the aspect ratio
          </h3>
          <p class="mt-3 text-base leading-relaxed text-surface-foreground-2">
            Select from preset aspect ratios like 1:1 or 16:9, or customize the
            dimensions to fit your needs.
          </p>
        </div>
        <div>
          <h3
            class="text-2xl font-normal leading-normal text-surface-foreground-0"
          >
            Add details in your prompt
          </h3>
          <p class="mt-3 text-base leading-relaxed text-surface-foreground-2">
            Use a prompt to specify the area to expand or leave it blank to let
            the AI auto-fill.
          </p>
        </div>
        <div>
          <h3
            class="text-2xl font-normal leading-normal text-surface-foreground-0"
          >
            Click to expand
          </h3>
          <p class="mt-3 text-base leading-relaxed text-surface-foreground-2">
            Watch as your image is extended in just seconds.
          </p>
        </div>
        <div>
          <h3
            class="text-2xl font-normal leading-normal text-surface-foreground-0"
          >
            Download or upscale
          </h3>
          <p class="mt-3 text-base leading-relaxed text-surface-foreground-2">
            Choose among PNG, JPG, and SVG formats, or select
            <a
              class="cursor-pointer no-underline text-surface-foreground-accent-0 hover:text-piki-blue-400"
              href="https://www.freepik.com/ai/image-upscaler"
              >Upscale</a
            >
            for even higher resolution.
          </p>
        </div>
      </div>
    </div>
    <div class="$relative $order-1 $h-auto $w-full md:$order-2 md:$h-full">
      <img
        alt="How to expand images instantly"
        loading="lazy"
        width="746"
        height="564"
        decoding="async"
        data-nimg="1"
        class="$h-auto $w-full $rounded-lg $object-cover md:$h-full md:$rounded-none"
        style="color: transparent"
        srcset="https://cdn-front.freepik.com/landings/ai/image-extender/how-to/<EMAIL>?w=750&amp;h=1920&amp;q=75 750w, https://cdn-front.freepik.com/landings/ai/image-extender/how-to/<EMAIL>?w=1500&amp;h=3840&amp;q=90 1500w 1x, https://cdn-front.freepik.com/landings/ai/image-extender/how-to/<EMAIL>?w=1920&amp;h=1920&amp;q=75 1920w, https://cdn-front.freepik.com/landings/ai/image-extender/how-to/<EMAIL>?w=3840&amp;h=3840&amp;q=90 3840w 2x"
        src="https://cdn-front.freepik.com/landings/ai/image-extender/how-to/<EMAIL>?w=1920&amp;h=1920&amp;q=75 1920w, https://cdn-front.freepik.com/landings/ai/image-extender/how-to/<EMAIL>?w=3840&amp;h=3840&amp;q=90 3840w"
      />
    </div>
  </div>
  <a
    class="flex items-center justify-center gap-2 font-semibold transition duration-150 ease-in-out disabled:cursor-not-allowed disabled:opacity-50 disabled:aria-pressed:cursor-default disabled:aria-pressed:opacity-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-surface-border-10 active:outline-none text-nowrap text-base px-6 h-12 rounded bg-default-0 text-default-foreground-0 aria-pressed:bg-default-2 disabled:hover:bg-default-0 active:bg-default-2 disabled:active:bg-default-0 mt-12 hover:bg-default-1"
    data-cy="cta_link"
    href="https://www.freepik.com/pikaso/image-editor?tool=expand#from_element=landing_extender"
    >Expand images now</a
  >
</section>
