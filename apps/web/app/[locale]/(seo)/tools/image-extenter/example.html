<section class="relative mx-auto my-32 w-full max-w-screen-lg gap-8">
  <div class="px-5 lg:px-0">
    <div
      class="flex max-w-screen-2xl flex-col justify-between gap-16 md:flex-row md:items-end"
    >
      <div class="flex flex-col gap-2 w-full mx-auto">
        <h2
          class="text-3xl font-normal leading-snug text-surface-foreground-0 lg:text-5xl"
        >
          Top image expander solutions
        </h2>
      </div>
    </div>
  </div>
  <div class="mx-auto mt-8 max-w-screen-xl md:mt-10">
    <div>
      <div class="flex gap-4 px-5 lg:px-0">
        <button
          class="flex items-center gap-2 font-semibold transition duration-150 ease-in-out disabled:cursor-not-allowed disabled:opacity-50 disabled:aria-pressed:cursor-default disabled:aria-pressed:opacity-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-surface-border-10 active:outline-none text-nowrap text-base px-6 h-12 rounded-full bg-ghost-0 text-ghost-foreground-0 aria-pressed:bg-ghost-2 hover:bg-ghost-1 disabled:hover:bg-ghost-0 active:bg-ghost-2 disabled:active:bg-ghost-0 justify-start"
        >
          <p class="whitespace-nowrap">Social media</p></button
        ><button
          class="flex items-center gap-2 font-semibold transition duration-150 ease-in-out disabled:cursor-not-allowed disabled:opacity-50 disabled:aria-pressed:cursor-default disabled:aria-pressed:opacity-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-surface-border-10 active:outline-none text-nowrap text-base px-6 h-12 rounded-full bg-default-0 text-default-foreground-0 aria-pressed:bg-default-2 hover:bg-default-1 disabled:hover:bg-default-0 active:bg-default-2 disabled:active:bg-default-0 justify-start"
        >
          <p class="whitespace-nowrap">Product shots</p></button
        ><button
          class="flex items-center gap-2 font-semibold transition duration-150 ease-in-out disabled:cursor-not-allowed disabled:opacity-50 disabled:aria-pressed:cursor-default disabled:aria-pressed:opacity-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-surface-border-10 active:outline-none text-nowrap text-base px-6 h-12 rounded-full bg-ghost-0 text-ghost-foreground-0 aria-pressed:bg-ghost-2 hover:bg-ghost-1 disabled:hover:bg-ghost-0 active:bg-ghost-2 disabled:active:bg-ghost-0 justify-start"
        >
          <p class="whitespace-nowrap">Photography</p>
        </button>
      </div>
    </div>
    <div>
      <div>
        <div class="relative mt-10 px-5 lg:px-0">
          <div
            class="relative mx-auto grid max-w-screen-xs grid-cols-1 items-center justify-center gap-8 md:max-w-screen-xl md:grid-cols-2 lg:gap-16 xl:gap-28 pb-0"
          >
            <div class="_ayfhna0">
              <div class="max-w-[500px]">
                <p class="mb-1 text-2xl text-surface-foreground-4">
                  Expand your product images to add context, meet
                  platform-specific dimensions, and fit the requirements of
                  online stores, ads, or catalogs. With Expand, you can tweak
                  layouts, extend backgrounds, and ensure your shots look clean
                  and professional on any platform.
                </p>
              </div>
            </div>
            <div class="_ayfhna0">
              <img
                alt="image extender"
                loading="lazy"
                width="520"
                height="388"
                decoding="async"
                data-nimg="1"
                class="relative z-[1px] size-full rounded-lg"
                srcset="https://cdn-front.freepik.com/landings/ai/image-extender/tabs-content/<EMAIL>?w=640&amp;h=1920&amp;q=75 640w, https://cdn-front.freepik.com/landings/ai/image-extender/tabs-content/<EMAIL>?w=1280&amp;h=3840&amp;q=90 1280w 1x, https://cdn-front.freepik.com/landings/ai/image-extender/tabs-content/<EMAIL>?w=1080&amp;h=1920&amp;q=75 1080w, https://cdn-front.freepik.com/landings/ai/image-extender/tabs-content/<EMAIL>?w=2160&amp;h=3840&amp;q=90 2160w 2x"
                src="https://cdn-front.freepik.com/landings/ai/image-extender/tabs-content/<EMAIL>?w=1080&amp;h=1920&amp;q=75 1080w, https://cdn-front.freepik.com/landings/ai/image-extender/tabs-content/<EMAIL>?w=2160&amp;h=3840&amp;q=90 2160w"
                style="color: transparent"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
